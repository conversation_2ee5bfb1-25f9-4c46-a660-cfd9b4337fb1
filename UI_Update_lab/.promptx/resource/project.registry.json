{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-27T06:41:15.137Z", "updatedAt": "2025-08-27T06:41:15.140Z", "resourceCount": 3}, "resources": [{"id": "frontend-architecture-workflow", "source": "project", "protocol": "execution", "name": "Frontend Architecture Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/frontend-architect/execution/frontend-architecture-workflow.execution.md", "metadata": {"createdAt": "2025-08-27T06:41:15.139Z", "updatedAt": "2025-08-27T06:41:15.139Z", "scannedAt": "2025-08-27T06:41:15.139Z", "path": "role/frontend-architect/execution/frontend-architecture-workflow.execution.md"}}, {"id": "frontend-architect", "source": "project", "protocol": "role", "name": "Frontend Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/frontend-architect/frontend-architect.role.md", "metadata": {"createdAt": "2025-08-27T06:41:15.139Z", "updatedAt": "2025-08-27T06:41:15.139Z", "scannedAt": "2025-08-27T06:41:15.139Z", "path": "role/frontend-architect/frontend-architect.role.md"}}, {"id": "architectural-thinking", "source": "project", "protocol": "thought", "name": "Architectural Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-architect/thought/architectural-thinking.thought.md", "metadata": {"createdAt": "2025-08-27T06:41:15.140Z", "updatedAt": "2025-08-27T06:41:15.140Z", "scannedAt": "2025-08-27T06:41:15.140Z", "path": "role/frontend-architect/thought/architectural-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}