import { ref, toRefs } from 'vue'
import { useDictStore } from '@/store/modules/dict'
import { DictApi } from '@/api/system/dict'
import type { DictOption } from '@/types/system/dict'

/**
 * 字典数据获取Composable
 * 保持与RuoYi完全一致的用法和数据格式
 * 
 * @param dictTypes 字典类型数组
 * @returns 响应式字典数据对象
 * 
 * @example
 * // 单个字典
 * const { sys_normal_disable } = useDict('sys_normal_disable')
 * 
 * // 多个字典
 * const { sys_user_sex, sys_normal_disable } = useDict('sys_user_sex', 'sys_normal_disable')
 */
export function useDict(...dictTypes: string[]) {
  const dictStore = useDictStore()
  const result = ref<Record<string, DictOption[]>>({})

  // 立即执行函数，保持与RuoYi一致的调用方式
  return (() => {
    dictTypes.forEach((dictType) => {
      // 初始化为空数组
      result.value[dictType] = []
      
      // 先从缓存中获取
      const cachedDict = dictStore.getDict(dictType)
      if (cachedDict) {
        result.value[dictType] = cachedDict
      } else {
        // 缓存中没有，从API获取
        DictApi.getDictDataByType(dictType).then(response => {
          // 转换数据格式，保持与RuoYi一致
          const dictData = response.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue,
            elTagType: (item.listClass || '') as '' | 'success' | 'info' | 'warning' | 'danger',
            elTagClass: item.cssClass || ''
          }))
          
          // 更新响应式数据
          result.value[dictType] = dictData
          
          // 存入缓存
          dictStore.setDict(dictType, dictData)
        }).catch(error => {
          console.error(`获取字典数据失败: ${dictType}`, error)
          // 发生错误时保持空数组
          result.value[dictType] = []
        })
      }
    })
    
    // 返回响应式引用，保持与RuoYi一致的解构用法
    return toRefs(result.value)
  })()
}

/**
 * 刷新字典缓存
 * @param dictType 字典类型，不传则刷新所有缓存
 */
export function refreshDict(dictType?: string) {
  const dictStore = useDictStore()
  
  if (dictType) {
    // 刷新指定字典
    dictStore.removeDict(dictType)
  } else {
    // 刷新所有字典缓存
    dictStore.cleanDict()
  }
}

/**
 * 预加载字典数据
 * @param dictTypes 需要预加载的字典类型数组
 */
export async function preloadDict(dictTypes: string[]) {
  const dictStore = useDictStore()
  
  const promises = dictTypes.map(async (dictType) => {
    // 检查缓存中是否已存在
    if (!dictStore.getDict(dictType)) {
      try {
        const response = await DictApi.getDictDataByType(dictType)
        const dictData = response.data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue,
          elTagType: (item.listClass || '') as '' | 'success' | 'info' | 'warning' | 'danger',
          elTagClass: item.cssClass || ''
        }))
        
        dictStore.setDict(dictType, dictData)
      } catch (error) {
        console.error(`预加载字典数据失败: ${dictType}`, error)
      }
    }
  })
  
  await Promise.all(promises)
}

export default useDict
