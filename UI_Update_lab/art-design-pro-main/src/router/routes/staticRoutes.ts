import { AppRouteRecordRaw } from '../utils/utils'
import { RoutesAlias } from '../routesAlias'

/**
 * 静态路由配置
 * 不需要权限就能访问的路由
 *
 * 注意事项：
 * 1、path、name 不要和动态路由冲突，否则会导致路由冲突无法访问
 * 2、不需要登录就能访问的路由，在 meta 中添加 noLogin: true
 */
export const staticRoutes: AppRouteRecordRaw[] = [
  // 不需要登录就能访问的路由示例
  // {
  //   path: '/welcome',
  //   name: 'WelcomeStatic',
  //   component: () => import('@views/dashboard/console/index.vue'),
  //   meta: { title: 'menus.dashboard.title', noLogin: true }
  // },
  {
    path: RoutesAlias.Login,
    name: 'Login',
    component: () => import('@views/auth/login/index.vue'),
    meta: { title: 'menus.login.title', isHideTab: true, setTheme: true }
  },
  {
    path: RoutesAlias.Register,
    name: 'Register',
    component: () => import('@views/auth/register/index.vue'),
    meta: { title: 'menus.register.title', isHideTab: true, noLogin: true, setTheme: true }
  },
  {
    path: RoutesAlias.ForgetPassword,
    name: 'ForgetPassword',
    component: () => import('@views/auth/forget-password/index.vue'),
    meta: { title: 'menus.forgetPassword.title', isHideTab: true, noLogin: true, setTheme: true }
  },
  {
    path: '/403',
    name: 'Exception403',
    component: () => import('@views/exception/403/index.vue'),
    meta: { title: '403', noLogin: true }
  },

  {
    path: '/500',
    name: 'Exception500',
    component: () => import('@views/exception/500/index.vue'),
    meta: { title: '500', noLogin: true }
  },
  {
    path: '/outside',
    component: () => import('@views/index/index.vue'),
    name: 'Outside',
    meta: { title: 'menus.outside.title' },
    children: [
      {
        path: '/outside/iframe/:path',
        name: 'Iframe',
        component: () => import('@/views/outside/Iframe.vue'),
        meta: { title: 'iframe' }
      }
    ]
  },

  // ==================== 演示路由（从asyncRoutes迁移） ====================
  // Dashboard演示路由
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@views/index/index.vue'),
    meta: { title: 'menus.dashboard.title' },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: () => import('@views/dashboard/console/index.vue'),
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: false,
          fixedTab: true
        }
      },
      {
        path: 'analysis',
        name: 'Analysis',
        component: () => import('@views/dashboard/analysis/index.vue'),
        meta: {
          title: 'menus.dashboard.analysis',
          keepAlive: false
        }
      },
      {
        path: 'ecommerce',
        name: 'Ecommerce',
        component: () => import('@views/dashboard/ecommerce/index.vue'),
        meta: {
          title: 'menus.dashboard.ecommerce',
          keepAlive: false
        }
      }
    ]
  },

  // Templates演示路由
  {
    path: '/template',
    name: 'Template',
    component: () => import('@views/index/index.vue'),
    meta: {
      title: 'menus.template.title',
      icon: '&#xe860;'
    },
    children: [
      {
        path: 'cards',
        name: 'Cards',
        component: () => import('@views/template/cards/index.vue'),
        meta: {
          title: 'menus.template.cards',
          keepAlive: false
        }
      },
      {
        path: 'banners',
        name: 'Banners',
        component: () => import('@views/template/banners/index.vue'),
        meta: {
          title: 'menus.template.banners',
          keepAlive: false
        }
      },
      {
        path: 'charts',
        name: 'Charts',
        component: () => import('@views/template/charts/index.vue'),
        meta: {
          title: 'menus.template.charts',
          keepAlive: false
        }
      },
      {
        path: 'map',
        name: 'Map',
        component: () => import('@views/template/map/index.vue'),
        meta: {
          title: 'menus.template.map',
          keepAlive: true
        }
      },
      {
        path: 'chat',
        name: 'Chat',
        component: () => import('@views/template/chat/index.vue'),
        meta: {
          title: 'menus.template.chat',
          keepAlive: true
        }
      },
      {
        path: 'calendar',
        name: 'Calendar',
        component: () => import('@views/template/calendar/index.vue'),
        meta: {
          title: 'menus.template.calendar',
          keepAlive: true
        }
      },
      {
        path: 'pricing',
        name: 'Pricing',
        component: () => import('@views/template/pricing/index.vue'),
        meta: {
          title: 'menus.template.pricing',
          keepAlive: true,
          isFullPage: true
        }
      }
    ]
  },

  // Widgets演示路由
  {
    path: '/widgets',
    name: 'Widgets',
    component: () => import('@views/index/index.vue'),
    meta: {
      title: 'menus.widgets.title',
      icon: '&#xe81a;'
    },
    children: [
      {
        path: 'icon-list',
        name: 'IconList',
        component: () => import('@views/widgets/icon-list/index.vue'),
        meta: {
          title: 'menus.widgets.iconList',
          keepAlive: true
        }
      },
      {
        path: 'icon-selector',
        name: 'IconSelector',
        component: () => import('@views/widgets/icon-selector/index.vue'),
        meta: {
          title: 'menus.widgets.iconSelector',
          keepAlive: true
        }
      },
      {
        path: 'image-crop',
        name: 'ImageCrop',
        component: () => import('@views/widgets/image-crop/index.vue'),
        meta: {
          title: 'menus.widgets.imageCrop',
          keepAlive: true
        }
      },
      {
        path: 'excel',
        name: 'Excel',
        component: () => import('@views/widgets/excel/index.vue'),
        meta: {
          title: 'menus.widgets.excel',
          keepAlive: true
        }
      },
      {
        path: 'video',
        name: 'Video',
        component: () => import('@views/widgets/video/index.vue'),
        meta: {
          title: 'menus.widgets.video',
          keepAlive: true
        }
      },
      {
        path: 'count-to',
        name: 'CountTo',
        component: () => import('@views/widgets/count-to/index.vue'),
        meta: {
          title: 'menus.widgets.countTo',
          keepAlive: false
        }
      }
    ]
  },

  // ==================== 系统管理静态路由 ====================
  // 字典数据管理页面（需要静态路由支持参数传递）
  {
    path: '/system/dict-data',
    name: 'SystemDictData',
    component: () => import('@views/index/index.vue'),
    meta: { title: '字典数据', hidden: true },
    children: [
      {
        path: 'index/:dictId(\\d+)',
        name: 'DictDataDetail',
        component: () => import('@views/system/dict/data.vue'),
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },

  // Examples演示路由
  {
    path: '/examples',
    name: 'Examples',
    component: () => import('@views/index/index.vue'),
    meta: {
      title: 'menus.examples.title',
      icon: '&#xe8d4;',
      showBadge: true
    },
    children: [
      {
        path: 'tabs',
        name: 'Tabs',
        component: () => import('@views/examples/tabs/index.vue'),
        meta: {
          title: 'menus.examples.tabs'
        }
      },
      {
        path: 'tables/basic',
        name: 'TablesBasic',
        component: () => import('@views/examples/tables/basic.vue'),
        meta: {
          title: 'menus.examples.tablesBasic',
          keepAlive: true
        }
      },
      {
        path: 'tables',
        name: 'Tables',
        component: () => import('@views/examples/tables/index.vue'),
        meta: {
          title: 'menus.examples.tables',
          keepAlive: true,
          showBadge: true
        }
      },
      {
        path: 'form/search-bar',
        name: 'SearchBar',
        component: () => import('@views/examples/forms/search-bar.vue'),
        meta: {
          title: 'menus.examples.searchBar',
          keepAlive: true,
          showTextBadge: 'New'
        }
      },
      {
        path: 'tables/tree',
        name: 'TablesTree',
        component: () => import('@views/examples/tables/tree.vue'),
        meta: {
          title: 'menus.examples.tablesTree',
          keepAlive: true
        }
      },
      {
        path: 'dict-test',
        name: 'DictTest',
        component: () => import('@views/test/dict-test.vue'),
        meta: {
          title: '字典系统测试',
          keepAlive: true,
          showTextBadge: 'Test'
        }
      }
    ]
  }
]
