<template>
  <div class="editor-container">
    <!-- 简化版富文本编辑器，使用textarea作为基础 -->
    <div class="editor-toolbar">
      <el-button-group>
        <el-button size="small" @click="() => insertText('**粗体**')">粗体</el-button>
        <el-button size="small" @click="() => insertText('*斜体*')">斜体</el-button>
        <el-button size="small" @click="() => insertText('# 标题')">标题</el-button>
      </el-button-group>
      <el-upload
        :action="uploadUrl"
        :before-upload="handleBeforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        name="file"
        :show-file-list="false"
        :headers="headers"
        class="editor-img-uploader"
        v-if="type == 'url'"
      >
        <el-button size="small" type="primary">上传图片</el-button>
      </el-upload>
    </div>
    <div class="editor">
      <el-input
        ref="editorRef"
        v-model="content"
        type="textarea"
        :rows="rows"
        :placeholder="placeholder"
        :style="styles"
        @input="handleInput"
      />
    </div>
    <!-- 预览区域 -->
    <div v-if="showPreview" class="editor-preview" v-html="previewContent"></div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, getCurrentInstance } from 'vue'
  import { getToken } from '@/utils/auth'
  import { ElMessage } from 'element-plus'
  import type { UploadFile } from 'element-plus'

  interface Props {
    /** 编辑器的内容 */
    modelValue?: string
    /** 高度 */
    height?: number
    /** 最小高度 */
    minHeight?: number
    /** 只读 */
    readOnly?: boolean
    /** 上传文件大小限制(MB) */
    fileSize?: number
    /** 类型（base64格式、url格式） */
    type?: 'base64' | 'url'
    /** 占位符 */
    placeholder?: string
    /** 是否显示预览 */
    showPreview?: boolean
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void
  }

  const props = withDefaults(defineProps<Props>(), {
    height: 200,
    minHeight: 200,
    readOnly: false,
    fileSize: 5,
    type: 'url',
    placeholder: '请输入内容...',
    showPreview: false
  })

  const emit = defineEmits<Emits>()

  const { proxy } = getCurrentInstance()!
  const editorRef = ref()
  const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/common/upload')
  const headers = ref({
    Authorization: 'Bearer ' + getToken()
  })

  const content = ref('')
  const rows = computed(() => Math.max(Math.floor(props.height / 24), 5))

  const styles = computed(() => ({
    height: props.height + 'px',
    minHeight: props.minHeight + 'px'
  }))

  // 简单的markdown预览
  const previewContent = computed(() => {
    return content.value
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\n/g, '<br>')
  })

  watch(
    () => props.modelValue,
    (val) => {
      if (val !== content.value) {
        content.value = val || ''
      }
    },
    { immediate: true }
  )

  function handleInput(): void {
    emit('update:modelValue', content.value)
  }

  function insertText(text: string): void {
    const textarea = editorRef.value?.textarea || editorRef.value
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = content.value
      content.value = value.substring(0, start) + text + value.substring(end)
      emit('update:modelValue', content.value)

      // 设置光标位置
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + text.length, start + text.length)
      }, 0)
    }
  }

  function handleBeforeUpload(file: File): boolean {
    const isImg = /\.(jpg|jpeg|png|gif)$/i.test(file.name)
    if (!isImg) {
      ElMessage.error('上传文件只能是图片格式!')
      return false
    }
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize
      if (!isLt) {
        ElMessage.error(`上传文件大小不能超过 ${props.fileSize}MB!`)
        return false
      }
    }
    return true
  }

  function handleUploadSuccess(res: any, file: UploadFile): void {
    if (res.code === 200) {
      const imageUrl = import.meta.env.VITE_APP_BASE_API + res.fileName
      const imageMarkdown = `![${file.name}](${imageUrl})`
      insertText(imageMarkdown)
      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error(res.msg || '图片上传失败')
    }
  }

  function handleUploadError(): void {
    ElMessage.error('图片上传失败')
  }
</script>

<style scoped lang="scss">
  .editor-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .editor-toolbar {
    padding: 8px;
    border-bottom: 1px solid #dcdfe6;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .editor {
    :deep(.el-textarea__inner) {
      border: none;
      border-radius: 0;
      resize: vertical;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  .editor-preview {
    padding: 12px;
    border-top: 1px solid #dcdfe6;
    background-color: #fafafa;
    min-height: 100px;

    :deep(h1),
    :deep(h2),
    :deep(h3) {
      margin: 0 0 8px 0;
    }

    :deep(strong) {
      font-weight: bold;
    }

    :deep(em) {
      font-style: italic;
    }
  }

  .editor-img-uploader {
    display: inline-block;
  }
</style>
