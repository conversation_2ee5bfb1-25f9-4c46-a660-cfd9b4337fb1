<template>
  <el-button type="success" :loading="loading" @click="handleExport" v-bind="buttonProps">
    <el-icon v-if="!loading"><Download /></el-icon>
    {{ loading ? '导出中...' : buttonText }}
  </el-button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Download } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { FileApi } from '@/api/common/file'

  interface Props {
    /** 导出接口地址 */
    action: string
    /** 导出参数 */
    params?: Record<string, any>
    /** 按钮文本 */
    buttonText?: string
    /** 按钮属性 */
    buttonProps?: Record<string, any>
    /** 导出文件名 */
    filename?: string
    /** 是否在导出前确认 */
    confirmBeforeExport?: boolean
    /** 确认提示文本 */
    confirmText?: string
  }

  interface Emits {
    (e: 'before-export'): void
    (e: 'success'): void
    (e: 'error', error: any): void
  }

  const props = withDefaults(defineProps<Props>(), {
    buttonText: '导出数据',
    params: () => ({}),
    confirmBeforeExport: false,
    confirmText: '确定要导出数据吗？'
  })

  const emit = defineEmits<Emits>()

  const loading = ref(false)

  // 处理导出
  async function handleExport(): Promise<void> {
    try {
      // 导出前确认
      if (props.confirmBeforeExport) {
        const { ElMessageBox } = await import('element-plus')
        await ElMessageBox.confirm(props.confirmText, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }

      emit('before-export')
      loading.value = true

      await FileApi.exportExcel(props.action, props.params, props.filename)

      emit('success')
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('导出失败:', error)
        emit('error', error)
      }
    } finally {
      loading.value = false
    }
  }
</script>
