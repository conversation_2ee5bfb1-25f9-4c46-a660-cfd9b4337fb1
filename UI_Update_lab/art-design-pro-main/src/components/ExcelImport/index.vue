<template>
  <div class="excel-import">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :show-file-list="false"
      :headers="headers"
      accept=".xlsx,.xls"
      :disabled="loading"
    >
      <el-button type="primary" :loading="loading" v-bind="buttonProps">
        <el-icon v-if="!loading"><Upload /></el-icon>
        {{ loading ? '导入中...' : buttonText }}
      </el-button>
    </el-upload>

    <!-- 导入提示 -->
    <div class="import-tips" v-if="showTips">
      <p>导入说明：</p>
      <ul>
        <li>仅支持 .xlsx 和 .xls 格式文件</li>
        <li>文件大小不超过 {{ maxSize }}MB</li>
        <li v-if="templateUrl">
          <el-link @click="downloadTemplate" type="primary">点击下载导入模板</el-link>
        </li>
        <li v-if="updateSupport">支持更新已存在的数据</li>
      </ul>
    </div>

    <!-- 导入结果对话框 -->
    <el-dialog v-model="resultVisible" title="导入结果" width="600px" :close-on-click-modal="false">
      <div class="import-result">
        <div class="result-summary">
          <el-alert
            :type="importResult.success ? 'success' : 'error'"
            :title="importResult.message"
            show-icon
            :closable="false"
          />
        </div>

        <div v-if="importResult.data" class="result-details">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="总计">{{
              importResult.data.total || 0
            }}</el-descriptions-item>
            <el-descriptions-item label="成功">{{
              importResult.data.success || 0
            }}</el-descriptions-item>
            <el-descriptions-item label="失败">{{
              importResult.data.failure || 0
            }}</el-descriptions-item>
            <el-descriptions-item label="跳过">{{
              importResult.data.skip || 0
            }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div v-if="importResult.errors && importResult.errors.length > 0" class="error-list">
          <h4>错误详情：</h4>
          <el-table :data="importResult.errors" max-height="300" border>
            <el-table-column prop="row" label="行号" width="80" />
            <el-table-column prop="field" label="字段" width="120" />
            <el-table-column prop="message" label="错误信息" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="resultVisible = false">关闭</el-button>
        <el-button v-if="importResult.downloadUrl" type="primary" @click="downloadErrorFile">
          下载错误数据
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { Upload } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { getToken } from '@/utils/auth'
  import { FileApi } from '@/api/common/file'
  import type { UploadFile } from 'element-plus'

  interface Props {
    /** 导入接口地址 */
    action: string
    /** 按钮文本 */
    buttonText?: string
    /** 按钮属性 */
    buttonProps?: Record<string, any>
    /** 是否显示提示 */
    showTips?: boolean
    /** 最大文件大小(MB) */
    maxSize?: number
    /** 是否支持更新 */
    updateSupport?: boolean
    /** 模板下载地址 */
    templateUrl?: string
    /** 模板文件名 */
    templateName?: string
  }

  interface ImportResult {
    success: boolean
    message: string
    data?: {
      total: number
      success: number
      failure: number
      skip: number
    }
    errors?: Array<{
      row: number
      field: string
      message: string
    }>
    downloadUrl?: string
  }

  interface Emits {
    (e: 'success', result: any): void
    (e: 'error', error: any): void
  }

  const props = withDefaults(defineProps<Props>(), {
    buttonText: '导入数据',
    showTips: true,
    maxSize: 10,
    updateSupport: false
  })

  const emit = defineEmits<Emits>()

  const uploadRef = ref()
  const loading = ref(false)
  const resultVisible = ref(false)
  const importResult = ref<ImportResult>({
    success: false,
    message: ''
  })

  const uploadUrl = computed(() => {
    return import.meta.env.VITE_APP_BASE_API + props.action
  })

  const headers = computed(() => ({
    Authorization: 'Bearer ' + getToken()
  }))

  // 上传前验证
  function handleBeforeUpload(file: File): boolean {
    // 验证文件类型
    const isExcel = /\.(xlsx|xls)$/i.test(file.name)
    if (!isExcel) {
      ElMessage.error('只能上传 Excel 格式文件!')
      return false
    }

    // 验证文件大小
    const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
    if (!isLtMaxSize) {
      ElMessage.error(`文件大小不能超过 ${props.maxSize}MB!`)
      return false
    }

    loading.value = true
    return true
  }

  // 上传成功
  function handleUploadSuccess(response: any, file: UploadFile): void {
    loading.value = false

    if (response.code === 200) {
      importResult.value = {
        success: true,
        message: response.msg || '导入成功',
        data: response.data,
        errors: response.errors,
        downloadUrl: response.downloadUrl
      }
      resultVisible.value = true
      emit('success', response)
      ElMessage.success('导入成功')
    } else {
      importResult.value = {
        success: false,
        message: response.msg || '导入失败'
      }
      resultVisible.value = true
      emit('error', response)
      ElMessage.error(response.msg || '导入失败')
    }
  }

  // 上传失败
  function handleUploadError(error: any): void {
    loading.value = false
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请重试')
    emit('error', error)
  }

  // 下载模板
  function downloadTemplate(): void {
    if (props.templateUrl) {
      FileApi.downloadTemplate(props.templateUrl, props.templateName)
    }
  }

  // 下载错误文件
  function downloadErrorFile(): void {
    if (importResult.value.downloadUrl) {
      FileApi.downloadFile(importResult.value.downloadUrl, '导入错误数据.xlsx')
    }
  }
</script>

<style scoped lang="scss">
  .excel-import {
    .import-tips {
      margin-top: 12px;
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      font-size: 14px;
      color: #606266;

      p {
        margin: 0 0 8px 0;
        font-weight: 500;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
        }
      }
    }

    .import-result {
      .result-summary {
        margin-bottom: 16px;
      }

      .result-details {
        margin-bottom: 16px;
      }

      .error-list {
        h4 {
          margin: 0 0 12px 0;
          color: #f56c6c;
        }
      }
    }
  }
</style>
