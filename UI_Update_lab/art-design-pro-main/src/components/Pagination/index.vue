<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      :background="background"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { scrollTo } from '@/utils/scroll-to'

  interface Props {
    /** 总条目数 */
    total: number
    /** 当前页数 */
    page?: number
    /** 每页显示条目个数 */
    limit?: number
    /** 每页显示个数选择器的选项设置 */
    pageSizes?: number[]
    /** 移动端页码按钮的数量端默认值5 */
    pagerCount?: number
    /** 组件布局，子组件名用逗号分隔 */
    layout?: string
    /** 是否为分页按钮添加背景色 */
    background?: boolean
    /** 是否自动滚动到顶部 */
    autoScroll?: boolean
    /** 是否隐藏 */
    hidden?: boolean
  }

  interface Emits {
    (e: 'update:page', page: number): void
    (e: 'update:limit', limit: number): void
    (e: 'pagination', data: { page: number; limit: number }): void
  }

  const props = withDefaults(defineProps<Props>(), {
    page: 1,
    limit: 20,
    pageSizes: () => [10, 20, 30, 50],
    pagerCount: () => (document.body.clientWidth < 992 ? 5 : 7),
    layout: 'total, sizes, prev, pager, next, jumper',
    background: true,
    autoScroll: true,
    hidden: false
  })

  const emit = defineEmits<Emits>()

  const currentPage = computed({
    get() {
      return props.page
    },
    set(val: number) {
      emit('update:page', val)
    }
  })

  const pageSize = computed({
    get() {
      return props.limit
    },
    set(val: number) {
      emit('update:limit', val)
    }
  })

  function handleSizeChange(val: number): void {
    if (currentPage.value * val > props.total) {
      currentPage.value = 1
    }
    emit('pagination', { page: currentPage.value, limit: val })
    if (props.autoScroll) {
      scrollTo(0, 800)
    }
  }

  function handleCurrentChange(val: number): void {
    emit('pagination', { page: val, limit: pageSize.value })
    if (props.autoScroll) {
      scrollTo(0, 800)
    }
  }
</script>

<style scoped>
  .pagination-container {
    background: #fff;
  }
  .pagination-container.hidden {
    display: none;
  }
</style>
