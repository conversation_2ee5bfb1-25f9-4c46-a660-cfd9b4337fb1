<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="file-preview" v-loading="loading">
      <!-- 图片预览 -->
      <div v-if="isImage" class="image-preview">
        <el-image
          :src="fileUrl"
          fit="contain"
          style="width: 100%; max-height: 600px"
          :preview-src-list="[fileUrl]"
          preview-teleported
        />
      </div>

      <!-- PDF预览 -->
      <div v-else-if="isPdf" class="pdf-preview">
        <iframe :src="fileUrl" width="100%" height="600px" frameborder="0"></iframe>
      </div>

      <!-- 文本文件预览 -->
      <div v-else-if="isText" class="text-preview">
        <el-input v-model="textContent" type="textarea" :rows="20" readonly resize="none" />
      </div>

      <!-- 视频预览 */
      <div v-else-if="isVideo" class="video-preview">
        <video
          :src="fileUrl"
          controls
          style="width: 100%; max-height: 600px"
        >
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- 音频预览 */
      <div v-else-if="isAudio" class="audio-preview">
        <audio
          :src="fileUrl"
          controls
          style="width: 100%"
        >
          您的浏览器不支持音频播放
        </audio>
      </div>

      <!-- 不支持预览的文件 -->
      <div v-else class="unsupported-preview">
        <el-empty description="该文件类型不支持预览">
          <el-button type="primary" @click="downloadFile">
            <el-icon><Download /></el-icon>
            下载文件
          </el-button>
        </el-empty>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="downloadFile">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { Download } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { FileApi } from '@/api/common/file'

  interface Props {
    /** 是否显示预览 */
    modelValue: boolean
    /** 文件名 */
    fileName: string
    /** 文件URL */
    fileUrl?: string
    /** 预览标题 */
    title?: string
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const loading = ref(false)
  const textContent = ref('')

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const fileUrl = computed(() => {
    if (props.fileUrl) {
      return FileApi.getFileUrl(props.fileUrl)
    }
    return ''
  })

  const fileExtension = computed(() => {
    return props.fileName.split('.').pop()?.toLowerCase() || ''
  })

  const isImage = computed(() => {
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension.value)
  })

  const isPdf = computed(() => {
    return fileExtension.value === 'pdf'
  })

  const isText = computed(() => {
    return ['txt', 'md', 'json', 'xml', 'csv', 'log'].includes(fileExtension.value)
  })

  const isVideo = computed(() => {
    return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(fileExtension.value)
  })

  const isAudio = computed(() => {
    return ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a'].includes(fileExtension.value)
  })

  // 监听文件变化，加载文本内容
  watch([visible, isText], async ([newVisible, newIsText]) => {
    if (newVisible && newIsText && props.fileUrl) {
      await loadTextContent()
    }
  })

  // 加载文本内容
  async function loadTextContent(): Promise<void> {
    if (!props.fileUrl) return

    loading.value = true
    try {
      const response = await fetch(fileUrl.value)
      if (response.ok) {
        textContent.value = await response.text()
      } else {
        ElMessage.error('加载文件内容失败')
      }
    } catch (error) {
      console.error('加载文件内容失败:', error)
      ElMessage.error('加载文件内容失败')
    } finally {
      loading.value = false
    }
  }

  // 下载文件
  function downloadFile(): void {
    if (props.fileUrl) {
      FileApi.downloadFile(props.fileUrl, props.fileName)
    }
  }
</script>

<style scoped lang="scss">
  .file-preview {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;

    .image-preview,
    .pdf-preview,
    .text-preview,
    .video-preview,
    .audio-preview {
      width: 100%;
    }

    .text-preview {
      :deep(.el-textarea__inner) {
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .unsupported-preview {
      text-align: center;
      padding: 40px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
