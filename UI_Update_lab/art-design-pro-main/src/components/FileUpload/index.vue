<template>
  <div class="upload-file">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :data="data"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUploadRef"
      v-if="!disabled"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group
      ref="uploadFileListRef"
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
        v-for="(file, index) in fileList"
      >
        <el-link @click="() => handlePreview(file)" :underline="false" style="cursor: pointer">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link
            :underline="false"
            @click="() => handleDownload(file)"
            type="primary"
            v-if="!disabled"
            >&nbsp;下载</el-link
          >
          <el-link
            :underline="false"
            @click="() => handleDelete(index)"
            type="danger"
            v-if="!disabled"
            >&nbsp;删除</el-link
          >
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, onMounted, nextTick, getCurrentInstance } from 'vue'
  import { getToken } from '@/utils/auth'
  import { ElMessage, ElLoading } from 'element-plus'
  import type { UploadFile, UploadFiles } from 'element-plus'
  import { FileApi } from '@/api/common/file'

  interface FileItem {
    name: string
    url: string
    uid?: number
  }

  interface Props {
    /** 绑定值 */
    modelValue?: string | FileItem | FileItem[]
    /** 上传接口地址 */
    action?: string
    /** 上传携带的参数 */
    data?: Record<string, any>
    /** 数量限制 */
    limit?: number
    /** 大小限制(MB) */
    fileSize?: number
    /** 文件类型, 例如['png', 'jpg', 'jpeg'] */
    fileType?: string[]
    /** 是否显示提示 */
    isShowTip?: boolean
    /** 禁用组件（仅查看文件） */
    disabled?: boolean
    /** 拖动排序 */
    drag?: boolean
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void
  }

  const props = withDefaults(defineProps<Props>(), {
    action: '/common/upload',
    limit: 5,
    fileSize: 5,
    fileType: () => ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf'],
    isShowTip: true,
    disabled: false,
    drag: true
  })

  const emit = defineEmits<Emits>()

  const { proxy } = getCurrentInstance()!
  const number = ref(0)
  const uploadList = ref<FileItem[]>([])
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + props.action)
  const headers = ref({ Authorization: 'Bearer ' + getToken() })
  const fileList = ref<FileItem[]>([])
  const fileUploadRef = ref()
  const uploadFileListRef = ref()

  const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        let temp = 1
        // 首先将值转为数组
        const list = Array.isArray(val) ? val : (props.modelValue as string).split(',')
        // 然后将数组转为对象数组
        fileList.value = list.map((item) => {
          if (typeof item === 'string') {
            item = { name: item, url: item }
          }
          item.uid = item.uid || new Date().getTime() + temp++
          return item
        })
      } else {
        fileList.value = []
      }
    },
    { deep: true, immediate: true }
  )

  // 上传前校检格式和大小
  function handleBeforeUpload(file: File): boolean {
    // 校检文件类型
    if (props.fileType && props.fileType.length) {
      const fileName = file.name.split('.')
      const fileExt = fileName[fileName.length - 1]
      const isTypeOk = props.fileType.indexOf(fileExt) >= 0
      if (!isTypeOk) {
        ElMessage.error(`文件格式不正确，请上传${props.fileType.join('/')}格式文件!`)
        return false
      }
    }
    // 校检文件名是否包含特殊字符
    if (file.name.includes(',')) {
      ElMessage.error('文件名不正确，不能包含英文逗号!')
      return false
    }
    // 校检文件大小
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize
      if (!isLt) {
        ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`)
        return false
      }
    }
    ElLoading.service({ text: '正在上传文件，请稍候...' })
    number.value++
    return true
  }

  // 文件个数超出
  function handleExceed(): void {
    ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`)
  }

  // 上传失败
  function handleUploadError(err: Error): void {
    ElMessage.error('上传文件失败')
    ElLoading.service().close()
  }

  // 上传成功回调
  function handleUploadSuccess(res: any, file: UploadFile): void {
    if (res.code === 200) {
      uploadList.value.push({
        name: res.data?.originalFilename || res.fileName || file.name,
        url: res.data?.fileName || res.fileName
      })
      uploadedSuccessfully()
    } else {
      number.value--
      ElLoading.service().close()
      ElMessage.error(res.msg || '上传失败')
      fileUploadRef.value?.handleRemove(file)
      uploadedSuccessfully()
    }
  }

  // 删除文件
  function handleDelete(index: number): void {
    fileList.value.splice(index, 1)
    emit('update:modelValue', listToString(fileList.value))
  }

  // 下载文件
  function handleDownload(file: FileItem): void {
    if (file.url) {
      FileApi.downloadFile(file.url, file.name)
    }
  }

  // 预览文件
  function handlePreview(file: FileItem): void {
    if (file.url) {
      const previewUrl = FileApi.getFileUrl(file.url)
      window.open(previewUrl, '_blank')
    }
  }

  // 上传结束处理
  function uploadedSuccessfully(): void {
    if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList.value)
      uploadList.value = []
      number.value = 0
      emit('update:modelValue', listToString(fileList.value))
      ElLoading.service().close()
    }
  }

  // 获取文件名称
  function getFileName(name: string): string {
    // 如果是url那么取最后的名字 如果不是直接返回
    if (name.lastIndexOf('/') > -1) {
      return name.slice(name.lastIndexOf('/') + 1)
    } else {
      return name
    }
  }

  // 对象转成指定字符串分隔
  function listToString(list: FileItem[], separator = ','): string {
    let strs = ''
    for (let i in list) {
      if (list[i].url) {
        strs += list[i].url + separator
      }
    }
    return strs != '' ? strs.substring(0, strs.length - 1) : ''
  }

  // 初始化拖拽排序 - 暂时注释掉，需要安装sortablejs
  // onMounted(() => {
  //   if (props.drag && !props.disabled) {
  //     nextTick(() => {
  //       const element = uploadFileListRef.value?.$el || uploadFileListRef.value
  //       // Sortable.create(element, {
  //       //   ghostClass: 'file-upload-darg',
  //       //   onEnd: (evt) => {
  //       //     const movedItem = fileList.value.splice(evt.oldIndex, 1)[0]
  //       //     fileList.value.splice(evt.newIndex, 0, movedItem)
  //       //     emit('update:modelValue', listToString(fileList.value))
  //       //   }
  //       // })
  //     })
  //   }
  // })
</script>

<style scoped lang="scss">
  .file-upload-darg {
    opacity: 0.5;
    background: #c8ebfb;
  }
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
    transition: none !important;
  }
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
</style>
