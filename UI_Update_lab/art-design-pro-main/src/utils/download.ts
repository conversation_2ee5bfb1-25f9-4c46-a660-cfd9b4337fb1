/**
 * 文件下载工具函数
 * 用于处理blob响应并触发浏览器下载
 */

import { ElMessage } from 'element-plus'

/**
 * 下载blob文件
 * @param blob - Blob对象或响应数据
 * @param filename - 文件名
 */
export function downloadBlob(blob: Blob, filename: string): void {
  try {
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理资源
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

/**
 * 从响应中提取文件名
 * @param response - axios响应对象
 * @returns 文件名
 */
export function getFilenameFromResponse(response: any): string {
  // 尝试从响应头中获取文件名
  const contentDisposition = response.headers?.['content-disposition']
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
    if (filenameMatch && filenameMatch[1]) {
      let filename = filenameMatch[1].replace(/['"]/g, '')
      // 处理URL编码的文件名
      try {
        filename = decodeURIComponent(filename)
      } catch {
        // 如果解码失败，使用原始文件名
      }
      return filename
    }
  }

  // 如果无法从响应头获取，返回默认文件名
  return `export_${new Date().getTime()}.xlsx`
}

/**
 * 处理导出响应并下载文件
 * @param response - axios响应对象
 * @param defaultFilename - 默认文件名（当无法从响应头获取时使用）
 */
export function handleExportResponse(response: any, defaultFilename?: string): void {
  try {
    // 检查响应是否为blob类型
    if (response.data instanceof Blob) {
      const filename = defaultFilename || getFilenameFromResponse(response)
      downloadBlob(response.data, filename)
    } else {
      console.error('响应数据不是Blob类型:', response.data)
      ElMessage.error('导出数据格式错误')
    }
  } catch (error) {
    console.error('处理导出响应失败:', error)
    ElMessage.error('处理导出响应失败')
  }
}

/**
 * 验证Blob数据是否有效
 * @param data - 要验证的数据
 * @returns 是否为有效的blob数据
 */
export function validateBlobData(data: any): boolean {
  return data instanceof Blob && data.size > 0
}

/**
 * 通用下载方法（兼容RuoYi格式）
 * @param url - 下载地址
 * @param params - 请求参数
 * @param filename - 文件名
 * @param config - 额外配置
 */
export async function download(
  url: string,
  params: Record<string, any> = {},
  filename?: string,
  config: any = {}
): Promise<void> {
  const { ElLoading } = await import('element-plus')

  const loading = ElLoading.service({
    text: '正在下载数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const { http } = await import('@/utils/http')

    const response = await http.post(url, params, {
      responseType: 'blob',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      ...config
    })

    if (validateBlobData(response.data)) {
      const downloadFilename = filename || getFilenameFromResponse(response)
      downloadBlob(response.data, downloadFilename)
    } else {
      // 处理错误响应
      const resText = await response.data.text()
      const rspObj = JSON.parse(resText)
      const errMsg = rspObj.msg || '下载失败'
      ElMessage.error(errMsg)
    }
  } catch (error) {
    console.error('下载文件出现错误:', error)
    ElMessage.error('下载文件出现错误，请联系管理员！')
  } finally {
    loading.close()
  }
}
