<template>
  <div class="config-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:config:add'" @click="handleAdd" v-ripple> 新增参数 </ElButton>
          <ElButton
            v-auth="'system:config:edit'"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'system:config:remove'"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:config:export'" @click="handleExport" v-ripple> 导出 </ElButton>
          <ElButton
            v-auth="'system:config:remove'"
            @click="handleRefreshCache"
            v-ripple
            type="danger"
          >
            刷新缓存
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="configId"
        :loading="loading"
        :columns="columns"
        :data="configList"
        :stripe="true"
        @selection-change="handleSelectionChange"
      />

      <!-- 分页组件 -->
      <div class="art-pagination">
        <ElPagination
          v-show="total > 0"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getTableData"
          @current-change="getTableData"
        />
      </div>

      <!-- 添加或修改参数配置对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="500px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElFormItem label="参数名称" prop="configName">
            <ElInput v-model="form.configName" placeholder="请输入参数名称" />
          </ElFormItem>
          <ElFormItem label="参数键名" prop="configKey">
            <ElInput v-model="form.configKey" placeholder="请输入参数键名" />
          </ElFormItem>
          <ElFormItem label="参数键值" prop="configValue">
            <ElInput
              v-model="form.configValue"
              type="textarea"
              placeholder="请输入参数键值"
              :rows="3"
            />
          </ElFormItem>
          <ElFormItem label="系统内置" prop="configType">
            <ElRadioGroup v-model="form.configType">
              <ElRadio value="Y">是</ElRadio>
              <ElRadio value="N">否</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3" />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTableColumns } from '@/composables/useTableColumns'
  import { useAuth } from '@/composables/useAuth'
  import { ConfigApi } from '@/api/system/config'
  import type { Config, ConfigQueryParams } from '@/types/system/dict'
  import type { FormInstance, FormRules } from 'element-plus'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

  defineOptions({ name: 'SystemConfig' })

  const { hasAuth } = useAuth()

  const loading = ref(false)
  const dialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    configName: '',
    configKey: '',
    configType: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 参数配置数据
  const configList = ref<Config[]>([])
  const total = ref(0)
  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 查询参数
  const queryParams = reactive<ConfigQueryParams>({
    pageNum: 1,
    pageSize: 10,
    configName: '',
    configKey: '',
    configType: ''
  })

  // 表单数据
  const form = reactive<Config>({
    configId: undefined,
    configName: '',
    configKey: '',
    configValue: '',
    configType: 'Y',
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    configName: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }],
    configKey: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }],
    configValue: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改参数' : '新增参数'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      ...initialSearchState
    })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(queryParams, {
      pageNum: 1,
      ...formFilters
    })
    getTableData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '参数名称',
      key: 'configName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '参数键名',
      key: 'configKey',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '系统内置',
      key: 'configType',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' }
      ]
    }
  ])

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      type: 'selection',
      width: 55
    },
    {
      prop: 'configId',
      label: '参数主键',
      width: 100
    },
    {
      prop: 'configName',
      label: '参数名称',
      minWidth: 120
    },
    {
      prop: 'configKey',
      label: '参数键名',
      minWidth: 120
    },
    {
      prop: 'configValue',
      label: '参数键值',
      minWidth: 120
    },
    {
      prop: 'configType',
      label: '系统内置',
      width: 100,
      formatter: (row: Config) => {
        return h(ElTag, { type: row.configType === 'Y' ? 'success' : 'info' }, () =>
          row.configType === 'Y' ? '是' : '否'
        )
      }
    },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 120,
      formatter: (row: Config) => row.remark || '--'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      formatter: (row: Config) => row.createTime || '--'
    },
    {
      prop: 'operation',
      label: '操作',
      width: 150,
      formatter: (row: Config) => {
        return h('div', [
          hasAuth('system:config:edit') &&
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleUpdate(row)
            }),
          hasAuth('system:config:remove') &&
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
        ])
      }
    }
  ])

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    loading.value = true
    try {
      const response = await ConfigApi.getConfigList(queryParams)
      // 从嵌套的RuoYi响应格式中提取数据：response.data.rows
      configList.value = (response.data as any)?.rows || response.rows || []
      total.value = (response.data as any)?.total || response.total || 0
    } catch (error) {
      console.error('获取参数配置列表失败:', error)
      ElMessage.error('获取参数配置列表失败')
    } finally {
      loading.value = false
    }
  }

  const handleRefresh = () => {
    console.log('参数配置管理刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      ...initialSearchState
    })
    getTableData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: Config[]) => {
    ids.value = selection.map((item) => item.configId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    dialogVisible.value = true
    isEdit.value = false
  }

  // 修改按钮操作
  const handleUpdate = async (row?: Config) => {
    resetForm()
    const configId = row?.configId || ids.value[0]

    try {
      const response = await ConfigApi.getConfigDetail(configId)
      const configData = (response.data as any)?.data || response.data
      Object.assign(form, configData)
      dialogVisible.value = true
      isEdit.value = true
    } catch (_error) {
      console.error('获取参数配置信息失败:', _error)
      ElMessage.error('获取参数配置信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row?: Config) => {
    const configIds = row?.configId ? [row.configId] : ids.value

    try {
      await ElMessageBox.confirm(
        `是否确认删除参数编号为"${configIds.join(',')}"的数据项？`,
        '系统提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await ConfigApi.deleteConfig(configIds.length === 1 ? configIds[0] : configIds)
      ElMessage.success('删除成功')
      getTableData()
    } catch (_error) {
      if (_error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 导出按钮操作
  const handleExport = async () => {
    try {
      const response = await ConfigApi.exportConfig(queryParams)
      const { handleExportResponse } = await import('@/utils/download')
      handleExportResponse(response, `config_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_error) {
      // console.error('导出失败:', _error)
      ElMessage.error('导出失败')
    }
  }

  // 刷新缓存按钮操作
  const handleRefreshCache = async () => {
    try {
      await ConfigApi.refreshConfigCache()
      ElMessage.success('刷新缓存成功')
    } catch (_error) {
      ElMessage.error('刷新缓存失败')
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.configId) {
            await ConfigApi.updateConfig(form)
            ElMessage.success('修改成功')
          } else {
            await ConfigApi.addConfig(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          getTableData()
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (_error) {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.configId = undefined
    form.configName = ''
    form.configKey = ''
    form.configValue = ''
    form.configType = 'Y'
    form.remark = ''

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }
</script>

<style lang="scss" scoped>
  .config-page {
    .dialog-footer {
      text-align: right;
    }

    .art-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
