<template>
  <div class="post-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:post:add'" @click="handleAdd" v-ripple> 新增岗位 </ElButton>
          <ElButton
            v-auth="'system:post:edit'"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'system:post:remove'"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:post:export'" @click="handleExport" v-ripple> 导出 </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="postId"
        :loading="loading"
        :columns="columns"
        :data="postList"
        :stripe="true"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      />

      <!-- 使用ArtTable内置分页，无需单独的分页组件 -->

      <!-- 添加或修改岗位对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="500px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElFormItem label="岗位名称" prop="postName">
            <ElInput v-model="form.postName" placeholder="请输入岗位名称" />
          </ElFormItem>
          <ElFormItem label="岗位编码" prop="postCode">
            <ElInput v-model="form.postCode" placeholder="请输入编码名称" />
          </ElFormItem>
          <ElFormItem label="岗位顺序" prop="postSort">
            <ElInputNumber
              v-model="form.postSort"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </ElFormItem>
          <ElFormItem label="岗位状态" prop="status">
            <ElRadioGroup v-model="form.status">
              <ElRadio value="0">正常</ElRadio>
              <ElRadio value="1">停用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3" />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, ref, reactive, computed, onMounted, h } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTableColumns } from '@/composables/useTableColumns'
  import { useAuth } from '@/composables/useAuth'
  import { PostApi } from '@/api/system/post'
  import type { Post, PostQueryParams } from '@/types/system/post'
  import type { FormInstance, FormRules } from 'element-plus'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

  defineOptions({ name: 'SystemPost' })

  const { hasAuth } = useAuth()

  const loading = ref(false)
  const dialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    postCode: '',
    postName: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 岗位数据
  const postList = ref<Post[]>([])
  const total = ref(0)
  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 分页配置
  const pagination = reactive({
    current: 1,
    size: 10,
    total: 0
  })

  // 查询参数
  const queryParams = reactive<PostQueryParams>({
    pageNum: 1,
    pageSize: 10,
    postCode: '',
    postName: '',
    status: ''
  })

  // 表单数据
  const form = reactive<Post>({
    postId: undefined,
    postCode: '',
    postName: '',
    postSort: 0,
    status: '0',
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    postName: [{ required: true, message: '岗位名称不能为空', trigger: 'blur' }],
    postCode: [{ required: true, message: '岗位编码不能为空', trigger: 'blur' }],
    postSort: [{ required: true, message: '岗位顺序不能为空', trigger: 'blur' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改岗位' : '新增岗位'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      ...initialSearchState
    })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(queryParams, {
      pageNum: 1,
      ...formFilters
    })
    getTableData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '岗位编码',
      key: 'postCode',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '岗位名称',
      key: 'postName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  ])

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      type: 'selection',
      width: 55
    },
    {
      prop: 'postId',
      label: '岗位编号',
      width: 100
    },
    {
      prop: 'postCode',
      label: '岗位编码',
      minWidth: 120
    },
    {
      prop: 'postName',
      label: '岗位名称',
      minWidth: 120
    },
    {
      prop: 'postSort',
      label: '岗位排序',
      width: 100
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      formatter: (row: Post) => {
        return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () =>
          row.status === '0' ? '正常' : '停用'
        )
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      formatter: (row: Post) => row.createTime || '--'
    },
    {
      prop: 'operation',
      label: '操作',
      width: 150,
      formatter: (row: Post) => {
        return h('div', [
          hasAuth('system:post:edit') &&
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleUpdate(row)
            }),
          hasAuth('system:post:remove') &&
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
        ])
      }
    }
  ])

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    loading.value = true
    try {
      const response = await PostApi.getPostList(queryParams)
      // 从嵌套的RuoYi响应格式中提取数据：response.data.rows
      postList.value = (response.data as any)?.rows || response.rows || []
      total.value = (response.data as any)?.total || response.total || 0
      pagination.total = total.value
      pagination.current = queryParams.pageNum || 1
      pagination.size = queryParams.pageSize || 10
    } catch (error) {
      console.error('获取岗位列表失败:', error)
      ElMessage.error('获取岗位列表失败')
    } finally {
      loading.value = false
    }
  }

  // 分页大小变化处理
  const handleSizeChange = (size: number) => {
    queryParams.pageSize = size
    queryParams.pageNum = 1
    pagination.size = size
    pagination.current = 1
    getTableData()
  }

  // 当前页变化处理
  const handleCurrentChange = (current: number) => {
    queryParams.pageNum = current
    pagination.current = current
    getTableData()
  }

  const handleRefresh = () => {
    console.log('岗位管理刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      ...initialSearchState
    })
    // 重置分页
    pagination.current = 1
    pagination.size = 10
    getTableData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: Post[]) => {
    ids.value = selection.map((item) => item.postId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    dialogVisible.value = true
    isEdit.value = false
  }

  // 修改按钮操作
  const handleUpdate = async (row?: Post) => {
    resetForm()
    const postId = row?.postId || ids.value[0]

    try {
      const response = await PostApi.getPostDetail(postId)
      const postData = (response.data as any)?.data || response.data
      Object.assign(form, postData)
      dialogVisible.value = true
      isEdit.value = true
    } catch (error) {
      console.error('获取岗位信息失败:', error)
      ElMessage.error('获取岗位信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row?: Post) => {
    const postIds = row?.postId ? [row.postId] : ids.value

    try {
      await ElMessageBox.confirm(
        `是否确认删除岗位编号为"${postIds.join(',')}"的数据项？`,
        '系统提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await PostApi.deletePost(postIds.length === 1 ? postIds[0] : postIds)
      ElMessage.success('删除成功')
      getTableData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 导出按钮操作
  const handleExport = async () => {
    try {
      const response = await PostApi.exportPost(queryParams)
      const { handleExportResponse } = await import('@/utils/download')
      handleExportResponse(response, `post_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.postId) {
            await PostApi.updatePost(form)
            ElMessage.success('修改成功')
          } else {
            await PostApi.addPost(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          getTableData()
        } catch {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.postId = undefined
    form.postCode = ''
    form.postName = ''
    form.postSort = 0
    form.status = '0'
    form.remark = ''

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }
</script>

<style lang="scss" scoped>
  .post-page {
    .dialog-footer {
      text-align: right;
    }

    .art-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
