<template>
  <ArtSearchBar
    ref="searchBarRef"
    v-model="formData"
    :items="formItems"
    :rules="rules"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'

  defineOptions({ name: 'UserSearch' })

  interface SearchForm {
    userName: string
    phonenumber: string
    status: string
    beginTime: string
    endTime: string
  }

  interface Props {
    modelValue: SearchForm
  }

  interface Emits {
    (e: 'update:modelValue', value: SearchForm): void
    (e: 'search'): void
    (e: 'reset'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const searchBarRef = ref()

  // 表单数据双向绑定
  const formData = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 校验规则
  const rules = {
    // userName: [{ required: true, message: '请输入用户名称', trigger: 'blur' }]
  }

  // 表单配置
  const formItems = computed(() => [
    {
      label: '用户名称',
      key: 'userName',
      type: 'input',
      placeholder: '请输入用户名称',
      clearable: true
    },
    {
      label: '手机号码',
      key: 'phonenumber',
      type: 'input',
      placeholder: '请输入手机号码',
      clearable: true,
      props: { maxlength: 11 }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      placeholder: '用户状态',
      clearable: true,
      props: {
        options: [
          { label: '正常', value: '0' },
          { label: '停用', value: '1' }
        ]
      }
    },
    {
      label: '创建时间',
      key: 'daterange',
      type: 'datetime',
      props: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD',
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期'
      }
    }
  ])

  /**
   * 重置操作
   */
  function handleReset() {
    emit('reset')
  }

  /**
   * 查询操作
   */
  async function handleSearch() {
    await searchBarRef.value?.validate()
    emit('search')
  }
</script>
