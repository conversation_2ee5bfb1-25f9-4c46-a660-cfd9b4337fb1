/**
 * 部门管理API
 * 基于RuoYi接口格式，支持完整的部门CRUD操作
 */

import { http } from '@/utils/http'
import type { Dept, DeptQueryParams } from '@/types/system/dept'
import type { RuoyiResponse } from '@/types/http'

export class DeptApi {
  /**
   * 查询部门列表
   */
  static getDeptList(params?: DeptQueryParams): Promise<RuoyiResponse<Dept[]>> {
    return http.get('/system/dept/list', { params })
  }

  /**
   * 查询部门详细信息
   */
  static getDeptDetail(deptId: number): Promise<RuoyiResponse<Dept>> {
    return http.get(`/system/dept/${deptId}`)
  }

  /**
   * 新增部门
   */
  static addDept(data: Dept): Promise<RuoyiResponse<any>> {
    return http.post('/system/dept', data)
  }

  /**
   * 修改部门
   */
  static updateDept(data: Dept): Promise<RuoyiResponse<any>> {
    return http.put('/system/dept', data)
  }

  /**
   * 删除部门
   */
  static deleteDept(deptId: number): Promise<RuoyiResponse<any>> {
    return http.delete(`/system/dept/${deptId}`)
  }

  /**
   * 查询部门下拉树结构
   */
  static getDeptTreeSelect(): Promise<RuoyiResponse<Dept[]>> {
    return http.get('/system/dept/treeselect')
  }

  /**
   * 根据角色ID查询部门树结构
   */
  static getRoleDeptTreeSelect(roleId: number): Promise<RuoyiResponse<Dept[]>> {
    return http.get(`/system/dept/roleDeptTreeselect/${roleId}`)
  }

  /**
   * 查询部门列表（排除节点）
   */
  static getDeptListExcludeChild(deptId: number): Promise<RuoyiResponse<Dept[]>> {
    return http.get(`/system/dept/list/exclude/${deptId}`)
  }
}

// 导出默认实例（兼容旧的调用方式）
export const {
  getDeptList,
  getDeptDetail,
  addDept,
  updateDept,
  deleteDept,
  getDeptTreeSelect,
  getRoleDeptTreeSelect,
  getDeptListExcludeChild
} = DeptApi

// 兼容RuoYi原有的函数名
export const listDept = getDeptList
export const getDept = getDeptDetail
export const delDept = deleteDept
export const listDeptExcludeChild = getDeptListExcludeChild
