/**
 * 参数配置API
 * 基于RuoYi接口格式，支持系统参数配置的完整CRUD操作
 */

import { http } from '@/utils/http'
import type { Config, ConfigQueryParams } from '@/types/system/dict'
import type { RuoyiResponse } from '@/types/http'

export class ConfigApi {
  /**
   * 查询参数配置列表
   */
  static getConfigList(params?: ConfigQueryParams): Promise<RuoyiResponse<Config>> {
    return http.get('/system/config/list', { params })
  }

  /**
   * 查询参数配置详细信息
   */
  static getConfigDetail(configId: number): Promise<RuoyiResponse<Config>> {
    return http.get(`/system/config/${configId}`)
  }

  /**
   * 根据参数键名查询参数值
   */
  static getConfigByKey(configKey: string): Promise<RuoyiResponse<string>> {
    return http.get(`/system/config/configKey/${configKey}`)
  }

  /**
   * 新增参数配置
   */
  static addConfig(data: Config): Promise<RuoyiResponse<any>> {
    return http.post('/system/config', data)
  }

  /**
   * 修改参数配置
   */
  static updateConfig(data: Config): Promise<RuoyiResponse<any>> {
    return http.put('/system/config', data)
  }

  /**
   * 修改参数键值
   */
  static updateConfigByKey(configKey: string, configValue: string): Promise<RuoyiResponse<any>> {
    return http.put('/system/config/updateByKey', { configKey, configValue })
  }

  /**
   * 删除参数配置
   */
  static deleteConfig(configIds: number | number[]): Promise<RuoyiResponse<any>> {
    return http.delete(`/system/config/${configIds}`)
  }

  /**
   * 导出参数配置
   */
  static exportConfig(params?: ConfigQueryParams): Promise<any> {
    return http.post('/system/config/export', params, { responseType: 'blob' })
  }

  /**
   * 刷新参数缓存
   */
  static refreshConfigCache(): Promise<RuoyiResponse<any>> {
    return http.delete('/system/config/refreshCache')
  }
}

// 导出默认实例（兼容旧的调用方式）
export const {
  getConfigList,
  getConfigDetail,
  getConfigByKey,
  addConfig,
  updateConfig,
  updateConfigByKey,
  deleteConfig,
  exportConfig,
  refreshConfigCache
} = ConfigApi

// 兼容RuoYi原有的函数名
export const listConfig = getConfigList
export const getConfig = getConfigDetail
export const delConfig = deleteConfig
export const refreshCache = refreshConfigCache
